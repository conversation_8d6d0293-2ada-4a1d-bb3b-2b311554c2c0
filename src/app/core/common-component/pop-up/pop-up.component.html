<ng-container>
    <div
        [ngClass]="(data.customTemplateRef == 'register')||(data.customTemplateRef == 'problem-report') ? 'invisible':'popup-container'">
        <div class="text-sm font-semibold flex justify-between pb-2 pop-up-header" #PopupHeader>
            <div *ngIf="data && data.title">{{ data.isEdit ? data.title[1] : data.title[0]}}</div>
            <div class="flex justify-end cursor-pointer" mat-dialog-close>
                <mat-icon class="text-base flex justify-end">close</mat-icon>
            </div>
        </div>
        <hr class="popUpCard">
        <ng-container *ngIf="!data.isCustom">
            <div exaiContainer class="w-full pt-4">
                <form [formGroup]="form" class="w-full">
                    <ng-container *ngFor="let model of formModel">
                        <dynamic-material-form-control *ngIf="!model.hidden"
                            class="w-full flex flex-col flex-wrap relative popUp-input text-xs" [group]="form"
                            [model]="model" [layout]="formLayout" (change)="onChange($event)" (blur)="onBlur($event)"
                            (focus)="onFocus($event)" (matEvent)="onMatEvent($event)">
                        </dynamic-material-form-control>
                    </ng-container>
                    <div #ButtonDiv class="flex justify-end pb-2">
                        <button class="btn-2 t-xs" type="submit" [disabled]="form.invalid" primary mat-button
                            *ngFor="let button of data.buttons; let btnIndex = index"
                            (click)="performOperation(button.requestDetails,btnIndex,form.value)">
                            <span
                                *ngIf="!(buttonObservableArray[btnIndex] | async); else bounce;">{{button.buttonText}}</span>
                        </button>
                    </div>
                </form>
            </div>
        </ng-container>
        <ng-container *ngIf="data.customTemplateRef == 'customAddPopupFormManageAllUsers'">
            <exai-custom-popup-for-manage-users [dialogRef]="dialogRef" [preResponse]="data.preResponse"
                [buttons]="data.buttons"
                (closeOutputEvent)="performOperation($event.requestDetails,$event.index,$event.response)">
            </exai-custom-popup-for-manage-users>
        </ng-container>
        <ng-container *ngIf="data.customTemplateRef == 'customAddPopupFormManageUserAccess'">
            <!-- <app-custom-add-popup-form-manage-user-access [data]="data.preResponse" [isEdit]="data.isEdit"
                (formSubmit)="performOperation(data.buttons[0].requestDetails, 0, $event)"
                (formCancel)="dialogRef.close()">
            </app-custom-add-popup-form-manage-user-access> -->
            <app-custom-add-popup-form-manage-user-access [dialogRef]="dialogRef" [preResponse]="data.preResponse"
                [buttons]="data.buttons"
                (closeOutputEvent)="performOperation($event.requestDetails,$event.index,$event.response)">
            </app-custom-add-popup-form-manage-user-access>
        </ng-container>
        <ng-container *ngIf="data.customTemplateRef == 'customAddPopupForTrainingInstitute'">
            <app-add-training-institute-popup [dialogRef]="dialogRef" [preResponse]="data.preResponse"
                [buttons]="data.buttons"
                (closeOutputEvent)="performOperation($event.requestDetails,$event.index,$event.response)">
            </app-add-training-institute-popup>
        </ng-container>
        <ng-container *ngIf="data.customTemplateRef == 'customAddPopupForAccItem'">
            <exai-custom-popup-for-acc-item [dialogRef]="dialogRef" [preResponse]="data.preResponse"
                [buttons]="data.buttons"
                (closeOutputEvent)="performOperation($event.requestDetails,$event.index,$event.response)">
            </exai-custom-popup-for-acc-item>
        </ng-container>
        <ng-container *ngIf="data.customTemplateRef == 'add-employer'">
            <app-employer-popup [dialogRef]="dialogRef" [preResponse]="data.preResponse" [buttons]="data.buttons"
                (closeOutputEvent)="performOperation($event.requestDetails,$event.index,$event.response)">
            </app-employer-popup>
        </ng-container>

        <ng-container *ngIf="data.customTemplateRef == 'add-employees'">
            <app-employees-popup [dialogRef]="dialogRef" [preResponse]="data.preResponse" [buttons]="data.buttons"
                (closeOutputEvent)="performOperation($event.requestDetails,$event.index,$event.response)">
            </app-employees-popup>
        </ng-container>



        <ng-container *ngIf="data.customTemplateRef == 'addApplicationForCandidate'">
            <exai-add-application-for-candidate [dialogRef]="dialogRef" [preResponse]="data.preResponse"
                [buttons]="data.buttons"
                (closeOutputEvent)="performOperation($event.requestDetails,$event.index,$event.response)">
            </exai-add-application-for-candidate>
        </ng-container>

        <ng-container *ngIf="data.customTemplateRef == 'addCandidate'">
            <exai-register [dialogRef]="dialogRef" [preResponse]="data.preResponse"
                (closeOutputEvent)="performOperation($event.requestDetails,$event.index,$event.response)">
            </exai-register>
        </ng-container>

        <!-- <ng-container *ngIf="data.customTemplateRef == 'addForm'">
            <exai-add-application-for-candidate [dialogRef]="dialogRef" [preResponse]="data.preResponse"
                (closeOutputEvent)="performOperation($event.requestDetails,$event.index,$event.response)">
            </exai-add-application-for-candidate>
            
            
        </ng-container> -->
        <ng-container *ngIf="data.customTemplateRef == 'register'">
            <ng-container (ngInit)="registerForExam()"></ng-container>
        </ng-container>

        <ng-container *ngIf="data.customTemplateRef == 'addForm'">
            <app-add-forms [dialogRef]="dialogRef" [preResponse]="data.preResponse"
                (closeOutputEvent)="performOperation($event.requestDetails,$event.index,$event.response)">

            </app-add-forms>
        </ng-container>

        <ng-container *ngIf="data.customTemplateRef == 'problem-report'">
            <ng-container (ngInit)="problemReport()"></ng-container>
        </ng-container>
    </div>
</ng-container>

<ng-template #bounce>
    <span class="spinner w-full">
        <div class="bounce1"></div>
        <div class="bounce2"></div>
        <div class="bounce3"></div>
    </span>
</ng-template>