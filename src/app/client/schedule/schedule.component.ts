import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnInit,
  ViewChild,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { Action, Store } from "@ngrx/store";
import moment from "moment";
import "moment-timezone";
import { CartItem } from "src/app/client/schedule/state/models/cartItem";
import { GlobalUserService } from "src/app/core/global-user.service";
import { ExamName } from "./state/models/examName";
import { Slot } from "./state/models/slot";
import { TimeSlotRange } from "./state/models/timeSlotRange";
import { Timezone } from "./state/models/timezone.model";
// import { Exam } from "./state/models/exam";
import { ConfirmationPopupComponent } from "src/app/core/common-component/confirmation-popup/confirmation-popup.component";
import {
  ClearTimeslots,
  getCart,
  getCartItems,
  getEligibilityroute,
  getExamId,
  getRegisteredExam,
  getTimeSlots,
  getTimezones,
  isPayment,
  removeCartItem,
  removeAllCartItem,
  reschedule,
  setCandidateId,
  setTimezome,
} from "./state/scheduled.actions";
import {
  get_candidateId,
  get_cartItems,
  get_eligibilityroute,
  get_examId,
  get_registeredexam,
  get_rescheduledResponse,
  get_timeslots,
  get_timezones,
} from "./state/scheduled.selectors";
import { get } from "lodash";
import { interval, Observable, Subject, Subscription } from "rxjs";
import {
  BreakpointObserver,
  Breakpoints,
  BreakpointState,
} from "@angular/cdk/layout";
import { map, takeUntil } from "rxjs/operators";
import { MatSidenav, MatSidenavContainer } from "@angular/material/sidenav";
import { SnackbarService } from "src/app/core/snackbar.service";
import { Reschedule } from "./state/models/makePayment";
import { ExamType } from "./state/models/examType";
import { SlotModel } from "./state/models/slot.model";
import { ExamTypeModel } from "./state/models/exam-type.model";
import {
  breadCrumbsSelector,
  recentLinkClickEventSelector,
} from "../dynamic-component-loader/dynamic-loader-store/dynamic-loader.selectors";
import {
  moduleTypes,
  Roles,
} from "../dynamic-component-loader/dynamic-component-loader.types";
import { NgDynamicBreadcrumbService } from "ng-dynamic-breadcrumb";
import { MatDialog, MatDialogConfig } from "@angular/material/dialog";
import { LanguageService } from "src/app/core/language.service";
import { DynamicPopupMesageComponent } from "src/app/core/common-component/pop-up/dynamic-popup-mesage/dynamic-popup-mesage.component";
import { HttpService } from "src/app/core/http.service";
import { HttpErrorResponse } from "@angular/common/http";
import { DynamicComponentLoaderComponent } from "../dynamic-component-loader/dynamic-component-loader.component";
import { setRecentLinkClickEvent } from "../dynamic-component-loader/dynamic-loader-store/dynamic-loader.actions";
import { cellClickEvent } from "src/app/core/common-component/table/dynamic-table-types";
import { crumb } from "src/app/core/common-component/dynamic-bread-crumbs/dynamic-bread-crumbs.types";
import { DatePipe } from "@angular/common";
import { Exam } from "./state/models/Exam";
import { StateAllowTestCenterforOnline } from "src/app/core/common-component/examroom-formbuilder/form-builder.types";
import { FormStatusToNames, FormTypes } from "../application/application.types";
@Component({
  selector: "app-schedule",
  templateUrl: "./schedule.component.html",
  styleUrls: ["./schedule.component.scss"],
  changeDetection: ChangeDetectionStrategy.Default,
})
export class ScheduleComponent implements OnInit {
  @ViewChild("drawer") drawer: any;
  @ViewChild("datepicker") datePicker: any;
  @ViewChild(MatSidenavContainer, { static: true })
  date = new Date();
  ShowTestCenter: boolean = false;
  breadCrumbsArray: crumb[] = null;
  value: number = 0;
  eplasedDateTime: string;
  rescheduleloading: boolean = false;
  schedulingloading: boolean = false;
  recentLinkClickEvent: cellClickEvent = null;
  sidenavContainer: MatSidenavContainer;
  form: FormGroup;
  timezones: Timezone[] = [];
  examTenantTypes: ExamType[];
  selectedExamType: ExamType;
  examName: ExamName;
  examstatus: number;
  examTypeSeledted: any = "Online";
  timeslots: Slot[] = [];
  dateSelected: Date;
  RescheduleButton: boolean = true;
  navigate: string = null;
  timeZoneSelected: Timezone;
  clientORtenanatId: number;
  public examTypeModels: ExamTypeModel[] = [];
  timeSlotRange: TimeSlotRange[];
  slots: TimeSlotRange = new TimeSlotRange("", 0, [], "", "");
  selectedTimeSlot: Slot;
  examTypeDisable: boolean = false;
  previousRoute: string;
  candidateId: number;
  registeredExams: Exam[];
  personTenantRoleId: number;
  public selectedItem: string = "";
  count = 0;
  total: any;
  subtotal: number;
  totalAmount: any;
  listExam: Observable<any>;
  listExams;
  cartList;
  cartId: number;
  public isHandset$: Observable<boolean> = this.breakpointObserver
    .observe(Breakpoints.Handset)
    .pipe(map((result: BreakpointState) => result.matches));
  scheduleId: any;
  scheduledExam: Exam;
  myGroup: FormGroup;
  ExamTitle: string;
  cart: CartItem[];
  slotsAvaiable: SlotModel[] = [];
  isSelect: boolean = false;
  amount: any;
  scheduleEvent: Subject<any> = new Subject<any>();
  radioselect = new FormControl("Online");
  //https://credentiauatapi.examroom.ai/client/api/operationstaff/cart-items?personTenantRoleId=2&cartId=2
  //https://credentiauatapi.examroom.ai/client/api/operationstaff/cart-item?personTenantRoleId=9&cartItemId=3913
  dates;
  time;
  constructor(
    private store: Store,
    public global: GlobalUserService,
    private activatedRoute: ActivatedRoute,
    private cdr: ChangeDetectorRef,
    private breakpointObserver: BreakpointObserver,
    private snackbar: SnackbarService,
    private router: Router,
    private ngDynamicBreadcrumbService: NgDynamicBreadcrumbService,
    private changeDetectorRef: ChangeDetectorRef,
    private dialog: MatDialog,
    private lngSrvc: LanguageService,
    private http: HttpService,
    private datepipes: DatePipe
  ) {
    this.store.dispatch<Action>(getTimezones());
  }

  ngOnInit(): void {
    this.setSlot();
    this.store.select(recentLinkClickEventSelector).subscribe((data) => {
      if (data != null && data) {
        this.navigate = data.column.linkMetaData.navigateToLabel;
        this.examstatus = data.element.examStatusId;
        this.recentLinkClickEvent = data;
      } else {
        //  this.router.navigate(['loader','candidate-management'])
        if (!this.candidateId) {
          this.router.navigate(["loader", "candidate-management"]);
        }
      }
    });
    this.dateSelected = null;
    this.activatedRoute.paramMap.subscribe((x: any) => {
      if (x) {
        if (x.params.scheduleId) {
          this.scheduleId = x.params.scheduleId;
          this.ExamTitle = x.params.title;
        }
        this.candidateId = Number(x.params.candidateId);
        this.store.dispatch<Action>(
          setCandidateId({ candidateId: this.candidateId })
        );
      }
    });

    this.myGroup = new FormGroup({
      timeZoneControl: new FormControl(),
    });
    this.myGroup.valueChanges.subscribe((data) => {});

    this.form = new FormGroup({
      CardNumber: new FormControl(),
    });
    this.global.userDetails.subscribe((user) => {
      if (user) {
        this.personTenantRoleId = user.personTenantRoleId;
        this.store.dispatch<Action>(
          getCartItems({ personTenantRoleId: this.candidateId })
        );

        this.listExam = this.store.select(get_cartItems);
        this.listExam.subscribe((cartItems) => {
          this.subtotal = cartItems.reduce(
            (acc, val) => (acc += val.amount),
            0
          );
        });
      }
    });
    this.store.select(breadCrumbsSelector).subscribe((x: any) => {
      this.breadCrumbsArray = x;
    });

    this.store.select(get_candidateId).subscribe((candidateId) => {
      // this.store.dispatch<Action>(
      //   getEligibilityroute({ candidateId: candidateId })
      // );

      this.store.dispatch<Action>(
        getRegisteredExam({ candidateId: candidateId })
      );
    });

    // this.store.select(get_eligibilityroute).subscribe((route: ExamName) => {
    //   if (route) {
    //     this.examName = route;

    //     setTimeout(()=>{
    //       this.store.dispatch<Action>(
    //         getExamId({ eligibilityRouteId: route["id"],candidateId:this.candidateId })
    //       );
    //     },2000)

    //   }
    // });
    this.store.select(get_cartItems).subscribe((cartItems) => {
      this.cart = cartItems;
    });

    this.store.select(get_timezones).subscribe((timezones: Timezone[]) => {
      if (timezones) {
        this.timezones = timezones;
      }
    });
    this.getExamTypes();

    this.getElaspedTimeDate();

    this.store.select(get_examId).subscribe((examTypes: ExamType[]) => {
      if (examTypes.length > 0) {
        this.getExamTypes();
      }
    });

    this.store.select(get_registeredexam).subscribe((exams: Exam[]) => {
      if (exams && exams.length > 0) {
        this.registeredExams = exams;
        // let a= this.registeredExams.filter(x=>(x.mode !=='Test Center' && x.examStatusId ===this.global.ScheduledStatusId))
        // this.RescheduleButton = a.length > 0 ? false : true
        // this.listExams=this.cartList.filter(x=>this.registeredExams.find(y=>y.id == x.personEventId))
        this.total = this.listExams.reduce(
          (acc, val) => (acc += val.amount),
          0
        );
      } else {
      }
    });

    this.store.select(get_timeslots).subscribe((timeslots) => {
      if (timeslots) {
        this.slotsAvaiable = [];
        if (timeslots.length > 0) {
          this.timeslots = timeslots;
          this.timeslots.forEach((ele: Slot) => {
            let slotstring = `${ele.strSlotDate} ${ele.strSlotTime}`;
            let slotDate = new Date(Date.parse(slotstring));
            let slotmodel = new SlotModel({
              slotId: ele.slotId,
              availableSlots: ele.availableSlots,
              bookedSlots: ele.bookedSlots,
              slotDate: ele.slotDate,
              strSlotDate: ele.strSlotDate,
              strSlotTime: ele.strSlotTime,
              totalSlots: ele.totalSlots,
              slotDateTime: slotDate,
              slotDateUtc: ele.slotDateUtc,
            });
            this.slotsAvaiable.push(slotmodel);
          });
          this.slotsAvaiable = this.slotsAvaiable.sort(
            (a, b) =>
              new Date(a.slotDateTime).getTime() -
              new Date(b.slotDateTime).getTime()
          );

          this.slotsAvaiable.forEach((ele: SlotModel) => {
            const { slotDate } = ele;
            if (
              new Date(slotDate).getUTCHours() >= 0 &&
              new Date(slotDate).getUTCHours() < 4
            ) {
              this.timeSlotRange[0].data.push(ele);
            } else if (
              new Date(slotDate).getUTCHours() >= 4 &&
              new Date(slotDate).getUTCHours() < 8
            ) {
              this.timeSlotRange[1].data.push(ele);
            } else if (
              new Date(slotDate).getUTCHours() >= 8 &&
              new Date(slotDate).getUTCHours() < 12
            ) {
              this.timeSlotRange[2].data.push(ele);
            } else if (
              new Date(slotDate).getUTCHours() >= 12 &&
              new Date(slotDate).getUTCHours() < 16
            ) {
              this.timeSlotRange[3].data.push(ele);
            } else if (
              new Date(slotDate).getUTCHours() >= 16 &&
              new Date(slotDate).getUTCHours() < 20
            ) {
              this.timeSlotRange[4].data.push(ele);
            } else if (
              new Date(slotDate).getUTCHours() >= 20 &&
              new Date(slotDate).getUTCHours() <= 23 &&
              new Date(slotDate).getUTCMinutes() <= 59
            ) {
              this.timeSlotRange[5].data.push(ele);
            }
          });
          this.slots = this.timeSlotRange[this.slots.id];
          this.isSelect = true;
        } else if (this.slotsAvaiable.length == 0) {
          this.snackbar.callSnackbaronWarning(
            "No slots available for the selected date"
          );
          this.isSelect = false;
        }
      }
    });

    if (this.navigate != null) {
      // Check if user is Operation Staff and coming from Candidate Details or Training Service
      if (
        this.global.userDetails.value.roleId == Roles.OperationStaff &&
        (this.navigate == ExamTypesForHardcoding.Candidate_Details ||
          this.navigate == ExamTypesForHardcoding.Training_Service)
      ) {
        const breadcrumb = [
          {
            label: "Home",
            url: "/dashboard",
          },
          {
            label: "Manage Candidate",
            url: "/loader/candidate-management",
          },
          {
            label: "Candidate Details",
            url: "/loader/candidate-details",
          },
          {
            label: "Register",
            url: "",
          },
        ];
        this.ngDynamicBreadcrumbService.updateBreadcrumb(breadcrumb);
      }
      // Check if user is Operation Staff but NOT coming from Candidate Details
      else if (this.global.userDetails.value.roleId == Roles.OperationStaff) {
        // Store the current navigation path to prevent unwanted redirects
        this.previousRoute = this.navigate;

        // Only set Schedule Accommodations breadcrumb if explicitly coming from that page
        if (
          this.navigate.includes("schedule-accommodations") ||
          this.navigate.includes("schedule-accommodation")
        ) {
          const breadcrumb = [
            {
              label: "Home",
              url: "/dashboard",
            },
            {
              label: "Schedule Accommodations",
              url: "/loader/schedule-accommodations",
            },
            {
              label: "View Details",
              url: "/loader/schedule-accommodation-details",
            },
            {
              label: "Register",
              url: "",
            },
          ];
          this.ngDynamicBreadcrumbService.updateBreadcrumb(breadcrumb);
        } else {
          // For other navigation paths, use a generic breadcrumb
          const breadcrumb = [
            {
              label: "Home",
              url: "/dashboard",
            },
            {
              label: "Schedule Exam",
              url: "",
            },
          ];
          this.ngDynamicBreadcrumbService.updateBreadcrumb(breadcrumb);
        }
      }
    }

    this.openDailogforNo_slot_avaiable();
  }

  getExamTypes() {
    this.http.getExamId(this.candidateId).subscribe((data: ExamName) => {
      if (data) {
        this.examName = data;
        this.http
          .getExamTypes(data.id, this.candidateId)
          .subscribe((examtype: ExamType[]) => {
            this.examTenantTypes = examtype;
            if (this.examTenantTypes.length > 0) {
              if (
                this.global.icon == "schedule" ||
                this.global.icon == "alarm_add"
              ) {
                this.global.icon = undefined;
                const data = examtype.find(
                  (ele) => ele.title == this.ExamTitle
                );
                data
                  ? (((this.examTypeDisable = true),
                    this.examIdSelectedEvent(data)),
                    this.selectedItem)
                  : null;
              } else {
                this.selectedExamType = null;
              }
            }
          });
      }
    });
  }

  examIdSelectedEvent(selectedExType: ExamType) {
    if (selectedExType) {
      this.ShowTestCenter =
        selectedExType.title === "Nurse Aide Skills Exam" &&
        StateAllowTestCenterforOnline.includes(
          this.global.personEventId.stateId
        )
          ? false
          : true;
      this.scheduleEvent.next(true);
      this.isSelect = false;
      this.myGroup.reset();
      this.selectedExamType = selectedExType;
      this.amount = selectedExType.price;
      this.store.dispatch<Action>(ClearTimeslots());
      this.slots.data = [];
      this.examTypeModels = [];
      this.examTypeModels = new ExamTypeModel().getDefaultValues();
      let isTypeAlreadySet = false;
      this.examTypeModels.forEach((x) => {
        let hasMode = selectedExType.supportedExamMode.find(
          (z) => z.examModeTypeId === x.id
        );
        if (hasMode) {
          if (!isTypeAlreadySet) {
            x.checked = true;
            isTypeAlreadySet = true;
          }
          x.disabled = false;
        } else {
          x.checked = false;
          x.disabled = true;
        }
      });
      let selectedParticularExamType = this.examTypeModels.find(
        (x) => x.checked === true
      );
      this.radioselect.setValue(selectedParticularExamType.name);
      this.examTypeSelectedEvent(selectedExType);
      this.myGroup.reset();
      this.dateSelected = null;
      this.datePicker.setDateToNull();
      this.timeZoneSelected = null;
    } else {
    }
  }

  navigateToParentUsingBreadCrumb2() {
    // extracting the second last
    let crumb = this.breadCrumbsArray[this.breadCrumbsArray.length - 1];
    if (crumb.navigationMetaData.navigateToType == moduleTypes.Custom)
      this.router.navigate([crumb.navigationMetaData.moduleID]);
    else {
      if (crumb.navigationMetaData.navigateToType == moduleTypes.Offspring) {
        this.recentLinkClickEvent = crumb.navigationMetaData.linkClickEvent;
      }
      this.store.dispatch(
        setRecentLinkClickEvent({ event: this.recentLinkClickEvent })
      );
      this.router.navigate(["loader", crumb.navigationMetaData.moduleID]);
    }
    // this.store.dispatch(PopFromBreadCrumbUptoIndex({ indexToPopUpto: this.breadCrumbsArray.indexOf(crumb) }));
  }

  navigateToParentUsingBreadCrumb() {
    // Check if we have a stored navigation path
    if (this.navigate) {
      if (
        this.navigate.includes("schedule-accommodations") ||
        this.navigate.includes("schedule-accommodation")
      ) {
        // Navigate back to Schedule Accommodations
        this.router.navigate(["loader", "schedule-accommodations"]);
      } else if (
        this.navigate == ExamTypesForHardcoding.Candidate_Details ||
        this.navigate == ExamTypesForHardcoding.Training_Service
      ) {
        // Navigate back to Candidate Details
        this.router.navigate(["loader", "candidate-details", this.candidateId]);
      } else {
        // Default navigation for Operation Staff
        this.router.navigate(["loader", "candidate-management"]);
      }
    } else {
      // Default fallback navigation
      this.router.navigate(["loader", "candidate-management"]);
    }
  }
  examTypeSelectedEvent($event) {
    this.examTypeSeledted = $event.value;
    this.dateSelected = null;
  }

  timeZoneSelectedEvent($event: Timezone) {
    this.setSlot();
    this.timeZoneSelected = $event;
    this.store.dispatch<Action>(
      setTimezome({ timezone: this.timeZoneSelected })
    );
    if (this.dateSelected)
      this.store.dispatch<Action>(
        getTimeSlots({
          timezone: this.timeZoneSelected.id,
          startDate: this.datepipes.transform(
            this.dateSelected,
            "yyyy-MM-dd",
            "+0000"
          ),
          examId: this.selectedExamType.id,
          offset: this.timeZoneSelected.offset,
        })
      );
  }

  dateChangedTemp() {}

  dateSelectedEvent($event) {
    if ($event.value) {
      this.setSlot();
      this.dateSelected = $event.value;
      if (this.timeZoneSelected && this.dateSelected)
        this.store.dispatch<Action>(
          getTimeSlots({
            timezone: this.timeZoneSelected.id,
            startDate: this.datepipes.transform(
              this.dateSelected,
              "yyyy-MM-dd",
              "+0000"
            ),
            examId: this.selectedExamType.id,
            offset: this.timeZoneSelected.offset,
          })
        );
    }
  }

  setSlot() {
    var t0 = new TimeSlotRange("12 AM - 04 AM", 0, [], "MIDNIGHT", "green");
    var t1 = new TimeSlotRange("04 AM - 08 AM", 1, [], "EARLY MORNING", "blue");
    var t2 = new TimeSlotRange("08 AM - 12 PM", 2, [], "MORNING", "blue");
    var t3 = new TimeSlotRange("12 PM - 04 PM", 3, [], "AFTERNOON", "blue");
    var t4 = new TimeSlotRange("04 PM - 08 PM", 4, [], "EVENING", "green");
    var t5 = new TimeSlotRange("08 PM - 11:59 PM", 5, [], "NIGHT", "green");
    this.timeSlotRange = [t0, t1, t2, t3, t4, t5];
  }

  slotRangeSelected($event) {
    this.slots = $event;
  }

  slotIdSelected($event) {
    this.selectedTimeSlot = $event;
  }

  openDailogforNo_slot_avaiable() {
    this.global.No_slots_Avaiable.subscribe((data: string) => {
      if (
        data != null &&
        data != "" &&
        data != undefined &&
        this.selectedExamType.title == "Nurse Aide Written Exam"
      ) {
        this.dialog
          .open(DynamicPopupMesageComponent, {
            data: {
              title: this.selectedExamType.title,
              message: this.lngSrvc.curLangObj.value.No_slot_avaiable,
              cancelButton: this.lngSrvc.curLangObj.value.no,
              OkButton: this.lngSrvc.curLangObj.value.yes,
            },
          })
          .afterClosed()
          .subscribe((confirmed: any) => {
            if (confirmed == true || confirmed.confirmed == true) {
              this.radioselect.setValue("Online");
              this.examTypeSelectedEvent({ value: "Online" });
              this.dialog.closeAll();
            } else {
              this.dialog.closeAll();
            }
          });
      }
    });
  }

  checkalreadyinCart(isPayment: boolean) {
    this.store.dispatch<Action>(
      getCartItems({ personTenantRoleId: this.candidateId })
    );
    if (this.cart.find((item) => item.personTenantRoleid != this.candidateId)) {
      this.dialog
        .open(DynamicPopupMesageComponent, {
          data: {
            title: this.lngSrvc.curLangObj.value.deletemsg2,
            message: this.lngSrvc.curLangObj.value.deletemsg,
            cancelButton: this.lngSrvc.curLangObj.value.no,
            OkButton: this.lngSrvc.curLangObj.value.yes,
          },
        })
        .afterClosed()
        .subscribe((confirmed: any) => {
          if (confirmed == true || confirmed.confirmed == true) {
            //delete cart and add to cart
            this.deleteAllItems();
            this.addToCart(isPayment);
          }
        });
    } else if (
      this.cart.find((item) => item.personTenantRoleid == this.candidateId) ||
      this.cart.length == 0
    ) {
      this.addToCart(isPayment);
    }
    this.drawer.toggle();
    if (this.drawer._opened) {
      this.store.dispatch<Action>(
        getCartItems({ personTenantRoleId: this.candidateId })
      );
      this.store.dispatch<Action>(
        getRegisteredExam({ candidateId: this.candidateId })
      );
    }
  }

  addToCart(isPayment: boolean) {
    // Fix for rescheduling after cancellation - check if navigate is "Training Program Details"
    if (this.navigate === "Training Program Details" && this.candidateId) {
      // Override the navigation path to ensure correct routing
      this.navigate = ExamTypesForHardcoding.Candidate_Details;
    }

    this.store.dispatch<Action>(
      getCart({
        details: {
          personTenantRoleId: this.personTenantRoleId,
          amount: this.amount,
          cartItemTypeId: 1,
          currencyId: 1,
          examDetail: {
            candidateId: this.candidateId,
            examId: this.selectedExamType.id,
            slotId: this.selectedTimeSlot.slotId,
            timeZone: this.timeZoneSelected.id,
            offSet: this.timeZoneSelected.offset,
            examModeId: 1,
            personTenantRoleId: this.personTenantRoleId,
            examDateTime: this.selectedTimeSlot.slotDateUtc,
          },
        },
        isPayment: isPayment,
      })
    );
    // Store the corrected navigation state
    localStorage.setItem("lastNavigationPath", this.navigate || "");

    setTimeout(() => {
      this.getExamTypes();
      this.store.dispatch<Action>(
        getCartItems({ personTenantRoleId: this.candidateId })
      );
    }, 2000);
  }

  pay() {
    // Store the current navigation context before navigating to payment
    localStorage.setItem("paymentSource", this.navigate || "");
    localStorage.setItem("candidateId", this.candidateId?.toString() || "");
    localStorage.setItem("isRescheduling", this.scheduleId ? "true" : "false");

    this.router.navigateByUrl(`scheduleExam/${this.candidateId}/payment/page`);
    this.store.dispatch<Action>(
      getCartItems({ personTenantRoleId: this.candidateId })
    );
  }

  public disable(data: ExamType) {
    if (this.examTenantTypes) {
      return (
        this.examTenantTypes.findIndex((x) => x.isDisabled && x.id == data.id) >
        -1
      );
    }
    return false;
  }

  closeSideNav() {
    if (this.drawer._mode == "side") {
      this.drawer.close();
    }
  }

  // getHardcodedDisabling(data: any) {
  //   // if (this.registeredExams.findIndex((x: any) => { return x.examName == data.title }) > -1)
  //   //   return false;
  //   switch (data) {
  //     case ExamTypesForHardcoding.NA_SE:
  //       return (this.registeredExams.findIndex((x: any) => { return (x.examName  == ExamTypesForHardcoding.NA_SE && x.examStatusId == this.global.ScheduledStatusId) || (x.examName  == ExamTypesForHardcoding.NA_SE && x.examStatusId == this.global.PaymentPendingStatusId)  ||(x.examName  == ExamTypesForHardcoding.NA_SE && x.examStatusId == this.global.scheduling_error) }) > -1);
  //     case ExamTypesForHardcoding.NA_OEE:
  //       return (this.registeredExams.findIndex((x: any) => { return (x.examName  == ExamTypesForHardcoding.NA_WE && x.examStatusId == this.global.ScheduledStatusId || x.examName == ExamTypesForHardcoding.NA_OSE && x.examStatusId == this.global.ScheduledStatusId) || (x.examName  == ExamTypesForHardcoding.NA_WE && x.examStatusId == this.global.PaymentPendingStatusId || x.examName == ExamTypesForHardcoding.NA_OSE && x.examStatusId == this.global.PaymentPendingStatusId)  ||(x.examName  == ExamTypesForHardcoding.NA_WE && x.examStatusId == this.global.scheduling_error|| x.examName == ExamTypesForHardcoding.NA_OSE && x.examStatusId == this.global.scheduling_error) }) > -1);
  //     case ExamTypesForHardcoding.NA_OSE:
  //       return (this.registeredExams.findIndex((x: any) => { return (x.examName == ExamTypesForHardcoding.NA_WE && x.examStatusId == this.global.ScheduledStatusId  || x.examName == ExamTypesForHardcoding.NA_OEE && x.examStatusId == this.global.ScheduledStatusId) || (x.examName == ExamTypesForHardcoding.NA_WE && x.examStatusId == this.global.PaymentPendingStatusId  || x.examName == ExamTypesForHardcoding.NA_OEE && x.examStatusId == this.global.PaymentPendingStatusId)  || (x.examName  == ExamTypesForHardcoding.NA_WE && x.examStatusId == this.global.scheduling_error|| x.examName == ExamTypesForHardcoding.NA_OSE && x.examStatusId == this.global.scheduling_error) }) > -1);
  //     case ExamTypesForHardcoding.NA_WE:
  //       return (this.registeredExams.findIndex((x: any) => { return (x.examName == ExamTypesForHardcoding.NA_OEE && x.examStatusId == this.global.ScheduledStatusId  || x.examName == ExamTypesForHardcoding.NA_OSE && x.examStatusId == this.global.ScheduledStatusId) || (x.examName == ExamTypesForHardcoding.NA_OEE && x.examStatusId == this.global.PaymentPendingStatusId  || x.examName == ExamTypesForHardcoding.NA_OSE && x.examStatusId == this.global.PaymentPendingStatusId)  || (x.examName  == ExamTypesForHardcoding.NA_OEE && x.examStatusId == this.global.scheduling_error|| x.examName == ExamTypesForHardcoding.NA_OSE && x.examStatusId == this.global.scheduling_error)}) > -1);
  //     default: return false;
  //   }
  // }

  getHardcodedDisabling(data: any) {
    // if (this.registeredExams.findIndex((x: any) => { return x.examName == data.title }) > -1)
    //   return false;
    switch (data) {
      case ExamTypesForHardcoding.NA_SE:
        return (
          this.registeredExams.findIndex((x: any) => {
            return (
              (x.examName == ExamTypesForHardcoding.NA_SE &&
                x.examStatusId == this.global.ScheduledStatusId) ||
              (x.examName == ExamTypesForHardcoding.NA_SE &&
                x.examStatusId == this.global.PaymentPendingStatusId) ||
              (x.examName == ExamTypesForHardcoding.NA_SE &&
                x.examStatusId == this.global.payment_Completed) ||
              (x.examName == ExamTypesForHardcoding.NA_SE &&
                x.examStatusId == this.global.scheduling_error) ||
              (x.examName == ExamTypesForHardcoding.NA_SE &&
                x.examStatusId == this.global.Exam_Completed &&
                x.isPassed)
            );
          }) > -1
        );
      case ExamTypesForHardcoding.NA_OEE:
        return (
          this.registeredExams.findIndex((x: any) => {
            return (
              (x.examName == ExamTypesForHardcoding.NA_WE &&
                x.examStatusId == this.global.ScheduledStatusId) ||
              (x.examName == ExamTypesForHardcoding.NA_OSE &&
                x.examStatusId == this.global.ScheduledStatusId) ||
              (x.examName == ExamTypesForHardcoding.NA_WE &&
                x.examStatusId == this.global.PaymentPendingStatusId) ||
              (x.examName == ExamTypesForHardcoding.NA_OSE &&
                x.examStatusId == this.global.PaymentPendingStatusId) ||
              (x.examName == ExamTypesForHardcoding.NA_WE &&
                x.examStatusId == this.global.payment_Completed) ||
              (x.examName == ExamTypesForHardcoding.NA_OSE &&
                x.examStatusId == this.global.payment_Completed) ||
              (x.examName == ExamTypesForHardcoding.NA_WE &&
                x.examStatusId == this.global.scheduling_error) ||
              (x.examName == ExamTypesForHardcoding.NA_OSE &&
                x.examStatusId == this.global.scheduling_error) ||
              (x.examName == ExamTypesForHardcoding.NA_WE &&
                x.examStatusId == this.global.Exam_Completed &&
                x.isPassed) ||
              (x.examName == ExamTypesForHardcoding.NA_OSE &&
                x.examStatusId == this.global.Exam_Completed &&
                x.isPassed)
            );
          }) > -1
        );
      case ExamTypesForHardcoding.NA_OSE:
        return (
          this.registeredExams.findIndex((x: any) => {
            return (
              (x.examName == ExamTypesForHardcoding.NA_WE &&
                x.examStatusId == this.global.ScheduledStatusId) ||
              (x.examName == ExamTypesForHardcoding.NA_OEE &&
                x.examStatusId == this.global.ScheduledStatusId) ||
              (x.examName == ExamTypesForHardcoding.NA_WE &&
                x.examStatusId == this.global.PaymentPendingStatusId) ||
              (x.examName == ExamTypesForHardcoding.NA_OEE &&
                x.examStatusId == this.global.PaymentPendingStatusId) ||
              (x.examName == ExamTypesForHardcoding.NA_WE &&
                x.examStatusId == this.global.payment_Completed) ||
              (x.examName == ExamTypesForHardcoding.NA_OSE &&
                x.examStatusId == this.global.payment_Completed) ||
              (x.examName == ExamTypesForHardcoding.NA_WE &&
                x.examStatusId == this.global.scheduling_error) ||
              (x.examName == ExamTypesForHardcoding.NA_OSE &&
                x.examStatusId == this.global.scheduling_error) ||
              (x.examName == ExamTypesForHardcoding.NA_WE &&
                x.examStatusId == this.global.Exam_Completed &&
                x.isPassed) ||
              (x.examName == ExamTypesForHardcoding.NA_OEE &&
                x.examStatusId == this.global.Exam_Completed &&
                x.isPassed)
            );
          }) > -1
        );
      case ExamTypesForHardcoding.NA_WE:
        return (
          this.registeredExams.findIndex((x: any) => {
            return (
              (x.examName == ExamTypesForHardcoding.NA_OEE &&
                x.examStatusId == this.global.ScheduledStatusId) ||
              (x.examName == ExamTypesForHardcoding.NA_OSE &&
                x.examStatusId == this.global.ScheduledStatusId) ||
              (x.examName == ExamTypesForHardcoding.NA_OEE &&
                x.examStatusId == this.global.PaymentPendingStatusId) ||
              (x.examName == ExamTypesForHardcoding.NA_OSE &&
                x.examStatusId == this.global.PaymentPendingStatusId) ||
              (x.examName == ExamTypesForHardcoding.NA_OEE &&
                x.examStatusId == this.global.payment_Completed) ||
              (x.examName == ExamTypesForHardcoding.NA_OSE &&
                x.examStatusId == this.global.payment_Completed) ||
              (x.examName == ExamTypesForHardcoding.NA_OEE &&
                x.examStatusId == this.global.scheduling_error) ||
              (x.examName == ExamTypesForHardcoding.NA_OSE &&
                x.examStatusId == this.global.scheduling_error) ||
              (x.examName == ExamTypesForHardcoding.NA_OSE &&
                x.examStatusId == this.global.Exam_Completed &&
                x.isPassed) ||
              (x.examName == ExamTypesForHardcoding.NA_OEE &&
                x.examStatusId == this.global.Exam_Completed &&
                x.isPassed)
            );
          }) > -1
        );
      default:
        return false;
    }
  }
  openCart() {
    this.drawer.toggle();
    if (this.drawer._opened) {
      this.store.dispatch<Action>(
        getCartItems({ personTenantRoleId: this.candidateId })
      );
      this.store.dispatch<Action>(
        getRegisteredExam({ candidateId: this.candidateId })
      );
    }
  }
  deleteItem(id): void {
    if (id) {
      this.store.dispatch<Action>(
        removeCartItem({
          cartItemId: id.personEventCartId,
          personTenantRoleId: this.candidateId,
        })
      );
      setTimeout(() => {
        this.store.dispatch<Action>(
          getRegisteredExam({ candidateId: this.candidateId })
        );
        this.store.dispatch<Action>(
          getCartItems({ personTenantRoleId: this.candidateId })
        );
        this.getExamTypes();
      }, 2000);
    }

    setTimeout(() => {
      this.store.dispatch<Action>(
        getRegisteredExam({ candidateId: this.candidateId })
      );
    }, 2000);
  }
  deleteAllItems() {
    if (this.cart) {
      this.store.dispatch<Action>(
        removeAllCartItem({
          cartId: this.cart[0].cartId,
          personTenantRoleId: this.candidateId,
        })
      );
      setTimeout(() => {
        this.getExamTypes();
        this.store.dispatch<Action>(
          getCartItems({ personTenantRoleId: this.candidateId })
        );
        this.store.dispatch<Action>(
          getRegisteredExam({ candidateId: this.candidateId })
        );
      }, 2000);
    }
  }

  retry_Schedule_Without_payment() {
    this.schedulingloading = true;
    const subs$: Subscription = interval(500).subscribe((res) => {
      this.value = this.value + 10;
      if (this.value === 150) {
        subs$.unsubscribe();
        this.schedulingloading = false;
        this.value = 0;
      }
    });

    let ScheduleDetails = {
      candidateId: this.candidateId,
      examId: this.selectedExamType.id,
      slotId: this.selectedTimeSlot.slotId,
      timeZone: this.timeZoneSelected.id,
      offSet: this.timeZoneSelected.offset,
      examModeId: 1,
      personTenantRoleId: this.global.userDetails.value.personTenantRoleId,
      testCenterId: "",
      accommodationType: "",
      accommodationItems: [],
      clientExamId: 0,
      testCenterName: "",
      testCenterAddress: "",
      testCenterCity: "",
      testCenterState: "",
      testCenterPostalCode: "",
      examDateTime: this.selectedTimeSlot.slotDateUtc,
      scheduleId: Number(this.scheduleId),
    };
    this.http.get_retry_schedule(ScheduleDetails).subscribe(
      (data: any) => {
        if (data) {
          var datePipe = new DatePipe("en-US");
          this.time = datePipe.transform(
            this.recentLinkClickEvent.element.examDateTime,
            "shortTime",
            "+0000"
          );
          let body = {
            body: `${this.recentLinkClickEvent.element.mode} ${
              this.recentLinkClickEvent.element.examName
            } scheduled on ${moment(
              this.recentLinkClickEvent.element.examDateTime
            ).format("MM/DD/YYYY")} at ${this.time} ${
              this.recentLinkClickEvent.element.timeZoneAbbreviation
            }  was reschedule by ${this.global.userDetails.value.roleName}`,
            candidateId: this.candidateId,
            files: [],
            id: 0,
            noteTypeid: 5,
            title: ` ${this.recentLinkClickEvent.element.mode} ${this.selectedExamType.title} Rescheduled`,
            userId: this.global.userDetails.value.personTenantRoleId,
            userName: `${this.recentLinkClickEvent.element.candidateName}`,
          };
          this.http.getAddnotes(body).subscribe((data) => {
            if (data) {
            }
          });
          this.snackbar.callSnackbaronSuccess(data.scheduledMessage);
          this.navigateToParentUsingBreadCrumb();
        }
      },
      (error: any) => {
        this.snackbar.callSnackbaronError(error.message.message.error);
        this.navigateToParentUsingBreadCrumb();
      }
    );
  }
  reschedule() {
    this.rescheduleloading = true;
    const subs$: Subscription = interval(500).subscribe((res) => {
      this.value = this.value + 10;
      if (this.value === 150) {
        subs$.unsubscribe();
        this.rescheduleloading = false;
        this.value = 0;
      }
    });

    var v: Reschedule = {
      candidateId: this.candidateId,
      examId: this.selectedExamType.id,
      slotId: this.selectedTimeSlot.slotId,
      timeZone: this.timeZoneSelected.id,
      offSet: this.timeZoneSelected.offset,
      personTenantRoleId: this.personTenantRoleId,
      scheduleId: Number(this.scheduleId),
      examModeId: 1,
      examDateTime: this.selectedTimeSlot.slotDateUtc,
      testCenterId: "",
      testCenterName: "",
      testCenterAddress: "",
      testCenterCity: "",
      testCenterState: "",
      testCenterPostalCode: "",
    };
    this.store.dispatch<Action>(reschedule({ rescheduleBody: v }));
    this.store.select(get_rescheduledResponse).subscribe((data: number) => {
      if (data != null && data) {
        var datePipe = new DatePipe("en-US");
        this.time = datePipe.transform(
          this.recentLinkClickEvent.element.examDateTime,
          "shortTime",
          "+0000"
        );
        let body = {
          body: `${this.recentLinkClickEvent.element.mode} ${
            this.recentLinkClickEvent.element.examName
          } scheduled on ${moment(
            this.recentLinkClickEvent.element.examDateTime
          ).format("MM/DD/YYYY")} at ${this.time} ${
            this.recentLinkClickEvent.element.timeZoneAbbreviation
          }  was reschedule by ${this.global.userDetails.value.roleName}`,
          candidateId: this.candidateId,
          files: [],
          id: 0,
          noteTypeid: 5,
          title: ` ${this.recentLinkClickEvent.element.mode} ${this.selectedExamType.title} Rescheduled`,
          userId: this.global.userDetails.value.personTenantRoleId,
          userName: `${this.recentLinkClickEvent.element.candidateName}`,
        };
        setTimeout(() => {
          this.http.getAddnotes(body).subscribe((data) => {
            if (data) {
            }
          });
          this.navigateToParentUsingBreadCrumb();
        });
      }
    });
  }

  getElaspedTimeDate() {
    this.http
      .getElapsedDateTime(this.global.personEventId.personTenantRoleId)
      .subscribe(
        (
          data: Array<{
            formTypeId: number;
            status: string;
            elapseDateTime: string;
          }>
        ) => {
          if (data) {
            let ElapesedApplication = data.filter(
              (x) =>
                x.formTypeId === FormTypes.Application &&
                x.status === FormStatusToNames[1]
            );
            this.eplasedDateTime =
              ElapesedApplication.length > 0
                ? ElapesedApplication[0].elapseDateTime
                : null;
          }
        }
      );
  }

  addExam() {
    this.drawer.close();

    // this.router.navigateByUrl(`scheduleExam/${this.candidateId}`)
    // this.router.navigate(['scheduleExam',this.recentLinkClickEvent.element.personTenantRoleId])
  }

  public ngOnDestroy(): void {
    this.store.dispatch(ClearTimeslots());
    this.isSelect = false;
  }
}
enum ExamTypesForHardcoding {
  NA_WE = "Nurse Aide Written Exam",
  NA_SE = "Nurse Aide Skills Exam",
  NA_OSE = "Nurse Aide Oral Spanish Exam",
  NA_OEE = "Nurse Aide Oral English Exam",
  Candidate_Details = "Candidate Details",
  Training_Service = "Training Program Details",
}
