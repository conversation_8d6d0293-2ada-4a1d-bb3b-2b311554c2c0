import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { ReactiveFormsModule } from "@angular/forms";
import { MatFormFieldModule } from "@angular/material/form-field";
import { MatSelectModule } from "@angular/material/select";
import { MatInputModule } from "@angular/material/input";
import { MatButtonModule } from "@angular/material/button";
import { MatSlideToggleModule } from "@angular/material/slide-toggle";
import { MatProgressSpinnerModule } from "@angular/material/progress-spinner";
import { MatCheckboxModule } from "@angular/material/checkbox";
import { MatAutocompleteModule } from "@angular/material/autocomplete";

import { CustomAddPopupFormManageUserAccessComponent } from "./custom-add-popup-form-manage-user-access/custom-add-popup-form-manage-user-access.component";
import { UserRolesTemplateComponent } from "./user-roles-template/user-roles-template.component";

@NgModule({
  declarations: [
    // Add your component to declarations
    CustomAddPopupFormManageUserAccessComponent,
    UserRolesTemplateComponent,
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatSelectModule,
    MatInputModule,
    MatButtonModule,
    MatSlideToggleModule,
    MatProgressSpinnerModule,
    MatCheckboxModule,
    MatAutocompleteModule,
  ],
  exports: [
    // Export your component
    CustomAddPopupFormManageUserAccessComponent,
    UserRolesTemplateComponent,
  ],
})
export class CustomTemplatesModule {}
