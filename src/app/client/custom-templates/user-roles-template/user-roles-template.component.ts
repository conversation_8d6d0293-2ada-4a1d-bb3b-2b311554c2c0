import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-user-roles-template',
  template: `
    <div class="roles-container">
      <div *ngFor="let role of userRoles" class="role-item">
        {{ role.roleName }}
      </div>
      <div *ngIf="!userRoles || userRoles.length === 0" class="no-roles">
        No roles assigned
      </div>
    </div>
  `,
  styles: [`
    .roles-container {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }
    .role-item {
      padding: 2px 8px;
      background-color: #f0f0f0;
      border-radius: 4px;
      font-size: 12px;
    }
    .no-roles {
      color: #999;
      font-style: italic;
      font-size: 12px;
    }
  `]
})
export class UserRolesTemplateComponent {
  @Input() data: any;
  
  get userRoles() {
    return this.data?.userRoles || [];
  }
}