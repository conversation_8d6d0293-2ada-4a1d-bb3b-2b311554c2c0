import { Component, OnInit, Input, Output, EventEmitter } from "@angular/core";
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from "@angular/forms";
import { HttpClient } from "@angular/common/http";
import { MatSnackBar } from "@angular/material/snack-bar";

@Component({
  selector: "app-custom-add-popup-form-manage-user-access",
  templateUrl: "./custom-add-popup-form-manage-user-access.component.html",
  styleUrls: ["./custom-add-popup-form-manage-user-access.component.scss"],
})
export class CustomAddPopupFormManageUserAccessComponent implements OnInit {
  @Input() data: any;
  @Input() isEdit: boolean = false;
  @Output() formSubmit: EventEmitter<any> = new EventEmitter();
  @Output() formCancel: EventEmitter<any> = new EventEmitter();

  userAccessForm: FormGroup;
  roles: any[] = [];
  users: any[] = [];
  isLoading: boolean = false;

  constructor(
    private fb: FormBuilder,
    private http: HttpClient,
    private snackBar: MatSnackBar
  ) {
    this.userAccessForm = this.fb.group({
      userId: ["", Validators.required],
      roleId: ["", Validators.required],
      active: [true],
    });
  }

  ngOnInit(): void {
    this.loadRoles();
    this.loadUsers();

    if (this.isEdit && this.data) {
      // Extract the first role from userRoles array for editing
      const roleId =
        this.data.userRoles && this.data.userRoles.length > 0
          ? this.data.userRoles[0].roleId
          : null;

      this.userAccessForm.patchValue({
        userId: this.data.userId || this.data.id,
        roleId: roleId,
        stateId: this.data.stateId,
        active: true, // Since active status is not in the response
      });

      // Disable userId field in edit mode
      this.userAccessForm.get("userId").disable();
    }
  }

  loadRoles() {
    this.isLoading = true;
    this.http.get("client/api/account/userroles").subscribe(
      (response: any) => {
        this.roles = response.data || [];
        this.isLoading = false;
      },
      (error) => {
        this.snackBar.open("Failed to load roles", "Close", { duration: 3000 });
        this.isLoading = false;
      }
    );
  }

  loadUsers() {
    this.isLoading = true;
    this.http.get("client/api/account/getusers?showAll=true").subscribe(
      (response: any) => {
        this.users = response.data || [];
        this.isLoading = false;
      },
      (error) => {
        this.snackBar.open("Failed to load users", "Close", { duration: 3000 });
        this.isLoading = false;
      }
    );
  }

  onSubmit() {
    if (this.userAccessForm.valid) {
      const formData = this.userAccessForm.getRawValue(); // Gets values including disabled fields

      if (this.isEdit) {
        formData.id = this.data.id; // Add ID for edit operations
      }

      this.formSubmit.emit(formData);
    } else {
      this.markFormGroupTouched(this.userAccessForm);
    }
  }

  onCancel() {
    this.formCancel.emit();
  }

  // Helper method to mark all controls as touched
  markFormGroupTouched(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach((control) => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }
}
