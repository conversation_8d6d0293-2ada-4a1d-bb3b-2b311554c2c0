import { Component, OnInit, Input, Output, EventEmitter } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { HttpClient } from "@angular/common/http";
import { MatSnackBar } from "@angular/material/snack-bar";
import { GlobalUserService } from "src/app/core/global-user.service";
import { MatDialogRef } from "@angular/material/dialog";
import {
  button,
  customPopup,
  submitEvent,
} from "src/app/core/common-component/pop-up/pop-up.types";
import { environment } from "src/environments/environment";
import lodash from "lodash";
import { Action, Store } from "@ngrx/store";
import { SnackbarService } from "src/app/core/snackbar.service";
import { HttpService } from "src/app/core/http.service";
import { creationSuccessfull } from "src/app/client/dynamic-component-loader/dynamic-loader-store/dynamic-loader.actions";
import {
  requestDetails,
  requestParam,
  Roles,
} from "src/app/client/dynamic-component-loader/dynamic-component-loader.types";
import { URL } from "src/app/core/url";
import { forkJoin } from "rxjs";

@Component({
  selector: "app-custom-add-popup-form-manage-user-access",
  templateUrl: "./custom-add-popup-form-manage-user-access.component.html",
  styleUrls: ["./custom-add-popup-form-manage-user-access.component.scss"],
})
export class CustomAddPopupFormManageUserAccessComponent
  implements OnInit, customPopup
{
  @Input() data: any;
  @Input() isEdit: boolean = false;
  @Output() formSubmit: EventEmitter<any> = new EventEmitter();
  @Output() formCancel: EventEmitter<any> = new EventEmitter();

  @Input() preResponse?: any = null;
  @Input() buttons?: Array<button>;
  @Input() dialogRef: MatDialogRef<any>;
  @Output() closeOutputEvent: EventEmitter<submitEvent> =
    new EventEmitter<submitEvent>();

  roles: any[] = [];
  users: any[] = [];
  states: any[] = [];
  isLoading: boolean = false;
  // userData: any;
  hide: boolean = false;

  Array = Array;
  Roles = Roles;
  userData: any;
  form: FormGroup;
  showAddress: boolean = false;
  rolesId: boolean = false;
  userRoles: Array<any> = [];
  isManageApplications;
  statesOrTrainingIns: Array<any> = [];
  CodeDetails: {
    tenantCode: string;
    testCenterId: number;
    testcenterGuid: string;
    testcenterName: string;
  };
  testCenterId: Array<any> = [];
  stateActive: boolean = false;

  constructor(
    private formBuilder: FormBuilder,
    private http: HttpClient,
    private snackBar: MatSnackBar,
    private globalUserService: GlobalUserService,
    private services: SnackbarService,
    private store: Store,
    private https: HttpService
  ) {}

  ngOnInit(): void {
    // Initialize form first
    this.initializeForm();

    this.globalUserService.userDetails.subscribe((data) => {
      if (data) {
        this.userData = data;
        // Load roles and states data
        this.loadRolesAndStates();
      }
    });
  }

  initializeForm() {
    this.form = this.formBuilder.group({
      id: [
        this.preResponse ? this.preResponse.personId || this.preResponse.id : 0,
      ],
      firstName: [
        this.preResponse ? this.preResponse.firstName : "",
        [Validators.required],
      ],
      middleName: [this.preResponse ? this.preResponse.middleName : ""],
      lastName: [
        this.preResponse ? this.preResponse.lastName : "",
        [Validators.required],
      ],
      email: [
        this.preResponse ? this.preResponse.emailId : "",
        [Validators.required, Validators.email],
      ],
      phoneNumber: [
        this.preResponse
          ? this.preResponse.phoneNumber || this.preResponse.contactNumber
          : "",
      ],
      role: [[], [Validators.required]],
      tenantId: [
        this.preResponse
          ? this.preResponse.stateId || this.preResponse.tenantId
          : "",
      ],
      trainingInstitute: [
        this.preResponse ? this.preResponse.trainingInstituteId : "",
      ],
      ClientStateCode: [""],
    });

    // Populate role data if available
    if (this.preResponse && this.preResponse.userRoles) {
      const roleIds = this.preResponse.userRoles.map((role) => role.roleId);
      this.form.patchValue({ role: roleIds });
    }
  }

  loadRolesAndStates() {
    forkJoin([
      // Try the general userroles endpoint first (should return all roles)
      this.http.get<any>(URL.BASE_URL + `Account/userroles`),
      this.http.get<any>(
        URL.BASE_URL +
          `state/states/persontenantroleid?persontenantRoleId=${this.globalUserService.userDetails.value.personTenantRoleId}&showAll=true`
      ),
    ]).subscribe(
      (responses: Array<any>) => {
        this.userRoles = responses[0];
        this.statesOrTrainingIns = responses[1];

        console.log("Loaded roles from userroles endpoint:", this.userRoles);
        console.log("Loaded states:", this.statesOrTrainingIns);

        // After loading data, populate the form if we have preResponse data
        if (this.preResponse) {
          this.populateFormWithData();
        }
      },
      (error) => {
        console.warn(
          "Failed to load from userroles endpoint, falling back to manageuserroles:",
          error
        );
        // Fallback to the original API if the new one fails
        this.loadRolesAndStatesFallback();
      }
    );

    // Set up form change listeners
    if (this.preResponse) {
      this.form.controls.role.valueChanges.subscribe(
        (selectedRoles: number[]) => {
          this.fetchStateORTrnIns();
        }
      );
    }
  }

  populateFormWithData() {
    if (!this.preResponse) return;

    // Extract role IDs from userRoles array
    const roleIds = this.preResponse.userRoles
      ? this.preResponse.userRoles.map((role: any) => role.roleId)
      : [];

    // Find the state ID - could be stateId or tenantId
    const stateId = this.preResponse.stateId || this.preResponse.tenantId;

    // Update form with the correct data
    this.form.patchValue({
      id: this.preResponse.personId || this.preResponse.id,
      firstName: this.preResponse.firstName || "",
      middleName: this.preResponse.middleName || "",
      lastName: this.preResponse.lastName || "",
      email: this.preResponse.emailId || "",
      phoneNumber:
        this.preResponse.phoneNumber || this.preResponse.contactNumber || "",
      role: roleIds,
      tenantId: stateId,
    });

    // Check if user's roles exist in available roles
    const availableRoleIds = this.userRoles.map((role) => role.id);
    const missingRoles = roleIds.filter(
      (roleId) => !availableRoleIds.includes(roleId)
    );
    const validRoles = roleIds.filter((roleId) =>
      availableRoleIds.includes(roleId)
    );

    console.log("Form populated with data:", {
      roleIds,
      stateId,
      availableRoles: this.userRoles,
      availableRoleIds,
      missingRoles,
      validRoles,
      formValue: this.form.value,
    });

    if (missingRoles.length > 0) {
      console.warn(
        "⚠️ Some user roles are not available in the dropdown:",
        missingRoles
      );
      console.warn("Available role IDs:", availableRoleIds);
      console.warn("User role IDs:", roleIds);
    }

    // Only set roles that are actually available in the dropdown
    const rolesToSet = validRoles.length > 0 ? validRoles : roleIds;

    // Force change detection for role dropdown
    setTimeout(() => {
      this.form.get("role")?.setValue(rolesToSet);
      console.log("Role set again:", rolesToSet, this.form.get("role")?.value);

      // Additional debugging
      const selectedOptions = this.userRoles.filter((role) =>
        rolesToSet.includes(role.id)
      );
      console.log("Selected role options:", selectedOptions);
    }, 100);
  }

  fetchStateORTrnIns(x: number = Roles.TrainingInstitue) {
    this.http
      .get<any>(
        URL.BASE_URL +
          `state/states/persontenantroleid?persontenantRoleId=${this.globalUserService.userDetails.value.personTenantRoleId}&showAll=true`
      )
      .subscribe((response: any) => {
        this.statesOrTrainingIns = response;
      });
  }

  state(event) {
    if (this.preResponse == null) {
      let states = this.statesOrTrainingIns.filter((x) => x.id === event.value);
      this.rolesId == true
        ? this.http
            .post(
              `${environment.baseUrl}client/api/exam/GetTestCentresForCredentia?stateCode=${states[0].stateCode}`,
              {}
            )
            .subscribe((data: Array<object>) => {
              if (data.length > 0) {
                this.testCenterId = data;
              }
            })
        : null;
    } else {
      setTimeout(() => {
        let states = this.statesOrTrainingIns.filter(
          (x) => x.id === event.value
        );
        this.rolesId == true
          ? this.http
              .post(
                `${environment.baseUrl}client/api/exam/GetTestCentresForCredentia?stateCode=${states[0].stateCode}`,
                {}
              )
              .subscribe((data: Array<object>) => {
                if (data.length > 0) {
                  this.testCenterId = data;
                }
              })
          : null;
      }, 1500);
    }
  }

  selectedRole(event: Event | any) {
    const selectedRoles: number[] = event?.value || [];
    const has = (ids: number[]) => selectedRoles.some((id) => ids.includes(id));
    const hasRole = (id: number) => selectedRoles.includes(id);
    if (has([17, 13, 8, 28])) {
      this.showAddress = false;
      this.stateActive = false;
      this.form.patchValue({
        address: "null",
        License: "null",
        cantest: true,
        zipcode: "null",
        city: "null",
        tenantId: this.globalUserService.userDetails.value.clientORtenantId,
      });
    } else {
      this.stateActive = true;
      this.rolesId = hasRole(4);
      this.form.patchValue(
        hasRole(4)
          ? {
              address: "",
              License: "null",
              cantest: true,
              zipcode: null,
              city: "",
            }
          : hasRole(22)
          ? {
              address: "",
              License: "",
              cantest: false,
              zipcode: null,
              city: "",
            }
          : {
              address: "null",
              License: "null",
              cantest: true,
              zipcode: "null",
              city: "null",
            }
      );
    }
    this.showAddress = has([22, 4]);
  }

  testcenterCode(code) {
    this.CodeDetails = code;
  }

  submit(requestDetails: requestDetails, index: number) {
    if (this.preResponse == null) {
      requestDetails?.requestParams?.forEach((x: requestParam) => {
        if (x.extractedFromGlobal) {
          x.paramValue =
            this.globalUserService.userDetails.value[
              x.elementPropertyToBeExtracted
            ];
        }
      });

      // if (this.form.value.permissionDetail == true) {
      //   this.form.value.permissionDetail =
      //     '{"Permission":{"ManageApplication":true}}';
      // } else
      //   this.form.value.permissionDetail =
      //     '{"Permission":{"ManageApplication":false}}';
      // if (this.preResponse) {
      //   this.form.value.personTenantRoleId =
      //     this.preResponse.personTenantRoleId;
      // }

      var formValueClone = lodash.cloneDeep(this.form.value);
      formValueClone.role = formValueClone.role;
      const selectedRoles: number[] = this.form.value.role || [];
      const hasRole = (id: number) => selectedRoles.includes(id);
      formValueClone = {
        ...formValueClone,
        testcenterGuid: this.CodeDetails?.testcenterGuid?.trim() || "",
        tenantCode: this.CodeDetails?.tenantCode?.trim() || "",
        testcenterName: this.CodeDetails?.testcenterName?.trim() || "",
        ClientStateCode:
          formValueClone.tenantId && !hasRole(Roles.TrainingInstitue)
            ? !hasRole(Roles.Employer)
              ? this.statesOrTrainingIns.find(
                  (x: any) => x.id === formValueClone.tenantId
                )?.stateCode
              : this.globalUserService.userDetails.value.stateCode
            : this.globalUserService.userDetails.value.stateCode,
      };

      this.closeOutputEvent.emit({
        response: formValueClone,
        requestDetails,
        index,
      });

      setTimeout(() => {
        this.store.dispatch<Action>(creationSuccessfull());
      }, 1500);
    } else {
      let userDetails: any = {
        id: this.preResponse.personId,
        firstName: this.form.value.firstName,
        middleName: this.form.value.middleName,
        lastName: this.form.value.lastName,
        email: this.preResponse.emailId,
        trainingInstitute: this.preResponse.trainingInstitute,
        ssn: this.preResponse.ssn,
        countryCode: "",
        country: "",
        city: this.preResponse.city,
        zipCode: this.preResponse.zipCode,
        accommodation: "",
        clientCandidateId: "",
        phoneNumber: this.form.value.phoneNumber,
        tenantId: this.preResponse.tenantId,
        profile: "",
        modifiedBy: 0,
        address: this.preResponse.address,
        addressLine1: "",
        addressLine2: "",
        dateofBirth: this.preResponse.dateofBirth,
        gender: this.preResponse.gender,
        stateName: this.preResponse.stateName,
        role: this.form.value.role,
        stateCode: this.globalUserService.userDetails.value.stateCode
          ? this.globalUserService.userDetails.value.stateCode
          : this.statesOrTrainingIns.find(
              (x: any) => x.id === this.preResponse.stateId
            )?.stateCode,
        personTenantRoleId: this.preResponse.personTenantRoleId,
        testCenterId:
          this.CodeDetails?.testCenterId != null &&
          this.CodeDetails?.testCenterId != undefined
            ? this.CodeDetails.testCenterId
            : null,
        testcenterGuid:
          this.CodeDetails?.testcenterGuid != null &&
          this.CodeDetails?.testcenterGuid != undefined &&
          this.CodeDetails?.testcenterGuid != ""
            ? this.CodeDetails.testcenterGuid
            : "",
        tenantCode:
          this.CodeDetails?.tenantCode != "" &&
          this.CodeDetails?.tenantCode != null &&
          this.CodeDetails?.tenantCode != undefined
            ? this.CodeDetails.tenantCode
            : null,
        testcenterName:
          this.CodeDetails?.testcenterName != "" &&
          this.CodeDetails?.testcenterName != undefined &&
          this.CodeDetails?.testcenterName != null
            ? this.CodeDetails.testcenterName
            : "",
        ClientStateCode: this.globalUserService.userDetails.value
          .clientORtenantId
          ? this.statesOrTrainingIns.find(
              (x: any) => x.id === this.preResponse.stateId
            )?.stateCode
          : this.globalUserService.userDetails.value.stateCode,
      };
      this.https
        .manageUserUpdate(this.preResponse.personTenantRoleId, userDetails)
        .subscribe(
          (data: any) => {
            if (data) {
              this.dialogRef.close();
              this.services.callSnackbaronSuccess("Update SuccessFully");
            }
            this.store.dispatch<Action>(creationSuccessfull());
          },
          (err: any) => {
            this.services.callSnackbaronError(`${err.message.message.error}`);
          }
        );
    }
  }

  // Helper method to mark all controls as touched
  markFormGroupTouched(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach((control) => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }
}

export interface addUserAccess {
  id?: number;
  email: string;
  trainingprogram: string;
  middleName: string;
  lastName: string;
  phoneNumber: string;
  role: number[];
  permissionDetail: string;
  tenantCode: string;
  trainingprogramcode: string;
  state: string;
  eligibilityroute: string[];
  expirydate: string;
  examtype: string;
  firstName: string;
  fax: string;
  website: string;
  zipcode: number;
  billingzipcode: number;
  address1: string;
  address2: string;
  billingaddress1: string;
  billingaddress2: string;
  city: string;
  billingcity: string;
}
