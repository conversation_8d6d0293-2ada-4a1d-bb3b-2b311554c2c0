import { Component, OnInit, Input, Output, EventEmitter } from "@angular/core";
import {
  FormBuilder,
  FormGroup,
  Validators,
  FormControl,
} from "@angular/forms";
import { HttpClient } from "@angular/common/http";
import { MatSnackBar } from "@angular/material/snack-bar";
import { GlobalUserService } from "src/app/core/global-user.service";
import { Observable } from "rxjs";
import {
  debounceTime,
  distinctUntilChanged,
  switchMap,
  startWith,
} from "rxjs/operators";
import { MatDialogRef } from "@angular/material/dialog";
import {
  button,
  customPopup,
  submitEvent,
} from "src/app/core/common-component/pop-up/pop-up.types";
import { environment } from "src/environments/environment";
import lodash from "lodash";
import { Action, Store } from "@ngrx/store";
import { SnackbarService } from "src/app/core/snackbar.service";
import { HttpService } from "src/app/core/http.service";
import { creationSuccessfull } from "src/app/client/dynamic-component-loader/dynamic-loader-store/dynamic-loader.actions";
import {
  requestDetails,
  requestParam,
  Roles,
} from "src/app/client/dynamic-component-loader/dynamic-component-loader.types";
import { URL } from "src/app/core/url";
import { forkJoin } from "rxjs";

@Component({
  selector: "app-custom-add-popup-form-manage-user-access",
  templateUrl: "./custom-add-popup-form-manage-user-access.component.html",
  styleUrls: ["./custom-add-popup-form-manage-user-access.component.scss"],
})
export class CustomAddPopupFormManageUserAccessComponent
  implements OnInit, customPopup
{
  @Input() data: any;
  @Input() isEdit: boolean = false;
  @Output() formSubmit: EventEmitter<any> = new EventEmitter();
  @Output() formCancel: EventEmitter<any> = new EventEmitter();

  @Input() preResponse?: any = null;
  @Input() buttons?: Array<button>;
  @Input() dialogRef: MatDialogRef<any>;
  @Output() closeOutputEvent: EventEmitter<submitEvent> =
    new EventEmitter<submitEvent>();

  roles: any[] = [];
  users: any[] = [];
  states: any[] = [];
  isLoading: boolean = false;
  // userData: any;
  hide: boolean = false;

  Array = Array;
  Roles = Roles;
  userData: any;
  form: FormGroup;
  showAddress: boolean = false;
  rolesId: boolean = false;
  userRoles: Array<any> = [];
  isManageApplications;
  statesOrTrainingIns: Array<any> = [];
  CodeDetails: {
    tenantCode: string;
    testCenterId: number;
    testcenterGuid: string;
    testcenterName: string;
  };
  testCenterId: Array<any> = [];
  stateActive: boolean = false;
  showTrainingInstitute: boolean = false;
  trainingInstitutes: Array<any> = [];
  filteredTrainingInstitutes: Observable<any[]>;
  trainingInstituteControl = new FormControl();

  constructor(
    private formBuilder: FormBuilder,
    private http: HttpClient,
    private snackBar: MatSnackBar,
    private globalUserService: GlobalUserService,
    private services: SnackbarService,
    private store: Store,
    private https: HttpService
  ) {}

  ngOnInit(): void {
    // Initialize form first
    this.initializeForm();

    this.globalUserService.userDetails.subscribe((data) => {
      if (data) {
        this.userData = data;
        // Load roles and states data
        this.loadRolesAndStates();
      }
    });
  }

  initializeForm() {
    this.form = this.formBuilder.group({
      id: [
        this.preResponse ? this.preResponse.personId || this.preResponse.id : 0,
      ],
      firstName: [
        this.preResponse ? this.preResponse.firstName : "",
        // Removed validators since field is read-only
      ],
      middleName: [this.preResponse ? this.preResponse.middleName : ""],
      lastName: [
        this.preResponse ? this.preResponse.lastName : "",
        // Removed validators since field is read-only
      ],
      email: [
        this.preResponse ? this.preResponse.emailId : "",
        // Removed validators since field is read-only
      ],
      role: [[], [Validators.required]],
      tenantId: [
        this.preResponse
          ? this.preResponse.stateId || this.preResponse.tenantId
          : "",
      ],
      trainingInstitute: [
        this.preResponse ? this.preResponse.trainingInstituteId : "",
      ],
      ClientStateCode: [""],
    });

    // Populate role data if available
    if (this.preResponse && this.preResponse.userRoles) {
      const roleIds = this.preResponse.userRoles.map((role) => role.roleId);
      this.form.patchValue({ role: roleIds });
    }
  }

  loadRolesAndStates() {
    forkJoin([
      // Try the general userroles endpoint first (should return all roles)
      this.http.get<any>(URL.BASE_URL + `Account/userroles`),
      this.http.get<any>(
        URL.BASE_URL +
          `state/states/persontenantroleid?persontenantRoleId=${this.globalUserService.userDetails.value.personTenantRoleId}&showAll=true`
      ),
    ]).subscribe(
      (responses: Array<any>) => {
        this.userRoles = responses[0];
        this.statesOrTrainingIns = responses[1];

        console.log("Loaded roles from userroles endpoint:", this.userRoles);
        console.log("Loaded states:", this.statesOrTrainingIns);

        // After loading data, populate the form if we have preResponse data
        if (this.preResponse) {
          this.populateFormWithData();
        }
      },
      (error) => {
        console.warn(
          "Failed to load from userroles endpoint, falling back to manageuserroles:",
          error
        );
        // Fallback to the original API if the new one fails
        this.loadRolesAndStatesFallback();
      }
    );

    // Set up form change listeners
    if (this.preResponse) {
      this.form.controls.role.valueChanges.subscribe(
        (selectedRoles: number[]) => {
          this.fetchStateORTrnIns();
        }
      );
    }
  }

  loadRolesAndStatesFallback() {
    forkJoin([
      this.http.get<any>(
        URL.BASE_URL +
          `Account/manageuserroles?personTenantRoleId=${this.globalUserService.userDetails.value.personTenantRoleId}&showAll=true`
      ),
      this.http.get<any>(
        URL.BASE_URL +
          `state/states/persontenantroleid?persontenantRoleId=${this.globalUserService.userDetails.value.personTenantRoleId}&showAll=true`
      ),
    ]).subscribe((responses: Array<any>) => {
      this.userRoles = responses[0];
      this.statesOrTrainingIns = responses[1];

      console.log(
        "Loaded roles from manageuserroles fallback:",
        this.userRoles
      );
      console.log("Loaded states:", this.statesOrTrainingIns);

      // After loading data, populate the form if we have preResponse data
      if (this.preResponse) {
        this.populateFormWithData();
      }
    });
  }

  populateFormWithData() {
    if (!this.preResponse) return;

    // Extract role IDs from userRoles array
    const roleIds = this.preResponse.userRoles
      ? this.preResponse.userRoles.map((role: any) => role.roleId)
      : [];

    // Find the state ID - could be stateId or tenantId
    const stateId = this.preResponse.stateId || this.preResponse.tenantId;

    // Update form with the correct data
    this.form.patchValue({
      id: this.preResponse.personId || this.preResponse.id,
      firstName: this.preResponse.firstName || "",
      middleName: this.preResponse.middleName || "",
      lastName: this.preResponse.lastName || "",
      email: this.preResponse.emailId || "",
      role: roleIds,
      tenantId: stateId,
    });

    // Check if user's roles exist in available roles
    const availableRoleIds = this.userRoles.map((role) => role.id);
    const missingRoles = roleIds.filter(
      (roleId) => !availableRoleIds.includes(roleId)
    );
    const validRoles = roleIds.filter((roleId) =>
      availableRoleIds.includes(roleId)
    );

    console.log("Form populated with data:", {
      roleIds,
      stateId,
      availableRoles: this.userRoles,
      availableRoleIds,
      missingRoles,
      validRoles,
      formValue: this.form.value,
    });

    if (missingRoles.length > 0) {
      console.warn(
        "⚠️ Some user roles are not available in the dropdown:",
        missingRoles
      );
      console.warn("Available role IDs:", availableRoleIds);
      console.warn("User role IDs:", roleIds);
    }

    // Only set roles that are actually available in the dropdown
    const rolesToSet = validRoles.length > 0 ? validRoles : roleIds;

    // Force change detection for role dropdown
    setTimeout(() => {
      this.form.get("role")?.setValue(rolesToSet);
      console.log("Role set again:", rolesToSet, this.form.get("role")?.value);

      // Additional debugging
      const selectedOptions = this.userRoles.filter((role) =>
        rolesToSet.includes(role.id)
      );
      console.log("Selected role options:", selectedOptions);

      // Check if Training Institute role is selected and show/hide field accordingly
      this.checkTrainingInstituteRole(rolesToSet);
    }, 100);
  }

  checkTrainingInstituteRole(selectedRoles: number[]) {
    // Training Institute role ID is 15 (from global service)
    const trainingInstituteRoleId = 15;
    this.showTrainingInstitute = selectedRoles.includes(
      trainingInstituteRoleId
    );

    console.log(
      "Training Institute role selected:",
      this.showTrainingInstitute
    );
    console.log("Selected roles:", selectedRoles);

    // Set up autocomplete when training institute field is shown
    if (this.showTrainingInstitute) {
      this.setupTrainingInstituteAutocomplete();
    }
  }

  setupTrainingInstituteAutocomplete() {
    this.filteredTrainingInstitutes =
      this.trainingInstituteControl.valueChanges.pipe(
        startWith(""),
        debounceTime(300),
        distinctUntilChanged(),
        switchMap((value) => {
          if (typeof value === "string" && value.length >= 2) {
            return this.searchTrainingInstitutes(value);
          } else {
            return [];
          }
        })
      );
  }

  searchTrainingInstitutes(searchText: string): Observable<any[]> {
    // Get current state ID from form or preResponse
    const stateId =
      this.form.get("tenantId")?.value || this.preResponse?.stateId || 4;

    console.log(
      "Searching training institutes with stateId:",
      stateId,
      "searchText:",
      searchText
    );

    return this.https.searchTrainingInstitutes(stateId, searchText);
  }

  onTrainingInstituteSelected(selectedInstitute: any) {
    console.log("Training institute selected:", selectedInstitute);
    this.form.patchValue({
      trainingInstitute:
        selectedInstitute.id || selectedInstitute.trainingInstituteId,
    });
  }

  displayTrainingInstitute(institute: any): string {
    console.log("Displaying training institute:", institute);
    return institute ? institute.name || institute.instituteName || "" : "";
  }

  fetchStateORTrnIns(x: number = Roles.TrainingInstitue) {
    this.http
      .get<any>(
        URL.BASE_URL +
          `state/states/persontenantroleid?persontenantRoleId=${this.globalUserService.userDetails.value.personTenantRoleId}&showAll=true`
      )
      .subscribe((response: any) => {
        this.statesOrTrainingIns = response;
      });
  }

  state(event) {
    if (this.preResponse == null) {
      let states = this.statesOrTrainingIns.filter((x) => x.id === event.value);
      this.rolesId == true
        ? this.http
            .post(
              `${environment.baseUrl}client/api/exam/GetTestCentresForCredentia?stateCode=${states[0].stateCode}`,
              {}
            )
            .subscribe((data: Array<object>) => {
              if (data.length > 0) {
                this.testCenterId = data;
              }
            })
        : null;
    } else {
      setTimeout(() => {
        let states = this.statesOrTrainingIns.filter(
          (x) => x.id === event.value
        );
        this.rolesId == true
          ? this.http
              .post(
                `${environment.baseUrl}client/api/exam/GetTestCentresForCredentia?stateCode=${states[0].stateCode}`,
                {}
              )
              .subscribe((data: Array<object>) => {
                if (data.length > 0) {
                  this.testCenterId = data;
                }
              })
          : null;
      }, 1500);
    }
  }

  selectedRole(event: Event | any) {
    const selectedRoles: number[] = event?.value || [];
    const has = (ids: number[]) => selectedRoles.some((id) => ids.includes(id));
    const hasRole = (id: number) => selectedRoles.includes(id);

    // Check if Training Institute role is selected
    this.checkTrainingInstituteRole(selectedRoles);
    if (has([17, 13, 8, 28])) {
      this.showAddress = false;
      this.stateActive = false;
      this.form.patchValue({
        address: "null",
        License: "null",
        cantest: true,
        zipcode: "null",
        city: "null",
        tenantId: this.globalUserService.userDetails.value.clientORtenantId,
      });
    } else {
      this.stateActive = true;
      this.rolesId = hasRole(4);
      this.form.patchValue(
        hasRole(4)
          ? {
              address: "",
              License: "null",
              cantest: true,
              zipcode: null,
              city: "",
            }
          : hasRole(22)
          ? {
              address: "",
              License: "",
              cantest: false,
              zipcode: null,
              city: "",
            }
          : {
              address: "null",
              License: "null",
              cantest: true,
              zipcode: "null",
              city: "null",
            }
      );
    }
    this.showAddress = has([22, 4]);
  }

  testcenterCode(code) {
    this.CodeDetails = code;
  }

  submit(requestDetails: requestDetails, index: number) {
    if (this.preResponse == null) {
      requestDetails?.requestParams?.forEach((x: requestParam) => {
        if (x.extractedFromGlobal) {
          x.paramValue =
            this.globalUserService.userDetails.value[
              x.elementPropertyToBeExtracted
            ];
        }
      });

      var formValueClone = lodash.cloneDeep(this.form.value);
      formValueClone.role = formValueClone.role;
      const selectedRoles: number[] = this.form.value.role || [];
      const hasRole = (id: number) => selectedRoles.includes(id);
      formValueClone = {
        ...formValueClone,
        testcenterGuid: this.CodeDetails?.testcenterGuid?.trim() || "",
        tenantCode: this.CodeDetails?.tenantCode?.trim() || "",
        testcenterName: this.CodeDetails?.testcenterName?.trim() || "",
        ClientStateCode:
          formValueClone.tenantId && !hasRole(Roles.TrainingInstitue)
            ? !hasRole(Roles.Employer)
              ? this.statesOrTrainingIns.find(
                  (x: any) => x.id === formValueClone.tenantId
                )?.stateCode
              : this.globalUserService.userDetails.value.stateCode
            : this.globalUserService.userDetails.value.stateCode,
      };

      this.closeOutputEvent.emit({
        response: formValueClone,
        requestDetails,
        index,
      });

      setTimeout(() => {
        this.store.dispatch<Action>(creationSuccessfull());
      }, 1500);
    } else {
      // Prepare request body for the new API endpoint
      const requestBody = {
        roleId: this.form.value.role || [],
        clientStateCode: this.globalUserService.userDetails.value.stateCode
          ? this.globalUserService.userDetails.value.stateCode
          : this.statesOrTrainingIns.find(
              (x: any) => x.id === this.preResponse.stateId
            )?.stateCode || "",
        trainingInstituteId: this.form.value.trainingInstitute || 0,
      };

      // Get user email for the endpoint URL
      const emailId = this.preResponse.emailId || "";

      console.log("Calling manageUserRoleUpdate with email:", emailId);
      console.log("Request body:", requestBody);

      this.https.manageUserRoleUpdate(emailId, requestBody).subscribe(
        (response: any) => {
          console.log("API Response:", response);
          if (response && response.success) {
            this.dialogRef.close();
            this.services.callSnackbaronSuccess(
              response.message || "Update Successful"
            );
          } else {
            this.services.callSnackbaronError(
              response.message || "Update failed"
            );
          }
          this.store.dispatch<Action>(creationSuccessfull());
        },
        (error: any) => {
          console.error("API Error:", error);
          this.services.callSnackbaronError(
            error?.error?.message ||
              error?.message ||
              "An error occurred while updating user access"
          );
        }
      );
    }
  }

  // Helper method to mark all controls as touched
  markFormGroupTouched(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach((control) => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }
}

export interface addUserAccess {
  id?: number;
  email: string;
  trainingprogram: string;
  middleName: string;
  lastName: string;
  phoneNumber: string;
  role: number[];
  permissionDetail: string;
  tenantCode: string;
  trainingprogramcode: string;
  state: string;
  eligibilityroute: string[];
  expirydate: string;
  examtype: string;
  firstName: string;
  fax: string;
  website: string;
  zipcode: number;
  billingzipcode: number;
  address1: string;
  address2: string;
  billingaddress1: string;
  billingaddress2: string;
  city: string;
  billingcity: string;
}
