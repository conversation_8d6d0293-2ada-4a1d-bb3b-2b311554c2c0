<div class="p-4">
  <form [formGroup]="userAccessForm" (ngSubmit)="onSubmit()">
    <div class="mb-4">
      <mat-form-field class="w-full">
        <mat-label>User</mat-label>
        <mat-select formControlName="userId">
          <mat-option *ngFor="let user of users" [value]="user.id">
            {{user.userName}} ({{user.email}})
          </mat-option>
        </mat-select>
        <mat-error *ngIf="userAccessForm.get('userId').hasError('required')">
          User is required
        </mat-error>
      </mat-form-field>
    </div>

    <div class="mb-4">
      <mat-form-field class="w-full">
        <mat-label>Role</mat-label>
        <mat-select formControlName="roleId">
          <mat-option *ngFor="let role of roles" [value]="role.id">
            {{role.roleName}}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="userAccessForm.get('roleId').hasError('required')">
          Role is required
        </mat-error>
      </mat-form-field>
    </div>

    <div class="mb-4">
      <mat-slide-toggle formControlName="active" color="primary">
        Active
      </mat-slide-toggle>
    </div>

    <div class="flex justify-end mt-4">
      <button type="button" mat-button (click)="onCancel()">Cancel</button>
      <button type="submit" mat-raised-button color="primary" [disabled]="userAccessForm.invalid || isLoading">
        <mat-spinner *ngIf="isLoading" [diameter]="20" class="mr-2"></mat-spinner>
        Submit
      </button>
    </div>
  </form>
</div>