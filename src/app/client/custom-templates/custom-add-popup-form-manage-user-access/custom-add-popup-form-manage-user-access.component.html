<!-- <div exaiContainer class="w-full pt-2 customPopUp">
  <form [formGroup]="form" class="w-full">

    <div class="flex justify-between mb-4 gap-2">
      <mat-form-field appearance="outline" class="flex-1 mr-2">
        <mat-label>First Name</mat-label>
        <input type="text" formControlName="firstName" matInput>
        <mat-error *ngIf="form.get('firstName').hasError('required')">
          First Name is required
        </mat-error>
      </mat-form-field>
      <mat-form-field appearance="outline" class="flex-1 mr-2">
        <mat-label>Middle Name</mat-label>
        <input type="text" formControlName="middleName" matInput>
      </mat-form-field>
      <mat-form-field appearance="outline" class="flex-1 mr-2">
        <mat-label>Last Name</mat-label>
        <input type="text" formControlName="lastName" matInput>
        <mat-error *ngIf="form.get('lastName').hasError('required')">
          Last Name is required
        </mat-error>
      </mat-form-field>
    </div>

    <div class="mb-4">
      <mat-form-field appearance="outline" class="w-full">
        <mat-label>Email</mat-label>
        <input type="email" formControlName="email" matInput>
        <mat-error *ngIf="form.get('email').hasError('required')">
          Email is required
        </mat-error>
        <mat-error *ngIf="form.get('email').hasError('email')">
          Please enter a valid email
        </mat-error>
      </mat-form-field>
    </div>


    <div class="mb-4">
      <mat-form-field appearance="outline" class="w-full">
        <mat-label>User Role</mat-label>
        <mat-select (selectionChange)="selectedRole($event)" formControlName="role" multiple>
          <mat-option *ngFor="let role of userRoles" [value]="role.id">
            {{role.roleName}}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="form.get('role').hasError('required')">
          User Role is required
        </mat-error>
      </mat-form-field>
    </div>

    <div class="mb-4">
      <mat-form-field appearance="outline" class="w-full">
        <mat-label>
          {{form.value.role == Roles.TrainingInstitue||(Array.isArray(form.value.role) &&
          form.value.role.includes(Roles.TrainingInstitue)) ? 'Training Institute' : 'State' }}
        </mat-label>
        <mat-select formControlName="tenantId" (selectionChange)="state($event)">
          <mat-option *ngFor="let state of statesOrTrainingIns" [value]="state.id">{{state.stateName ? state.stateName :
            state.name}}</mat-option>
        </mat-select>
      </mat-form-field>
    </div>

    <div class="mb-4">
      <mat-form-field appearance="outline" class="w-full">
        <mat-label>Training Institute</mat-label>
        <input type="tel" formControlName="phoneNumber" matInput>
        <mat-error *ngIf="form.get('trainingInstitute').hasError('required')">
          Training Institute is required
        </mat-error>
      </mat-form-field>
    </div>


    <div #ButtonDiv class="flex justify-end pb-2">
      <button class="btn-2 t-xs" type="submit" [disabled]="form.invalid" primary mat-button
        *ngFor="let button of buttons; let btnIndex = index" (click)="submit(button.requestDetails,btnIndex)">
        <span>{{button.buttonText}}</span>
      </button>
    </div>
  </form>
</div> -->


<div exaiContainer class="w-full pt-2 customPopUp">
  <form [formGroup]="form" class="w-full">
    <ng-container>
      <div fxLayout="row" class="flex justify-between pt-2">
        <mat-form-field appearance="outline" class="manageUsers mr-2">
          <mat-label>First Name</mat-label>
          <input type="text" formControlName="firstName" matInput readonly>
        </mat-form-field>
        <mat-form-field appearance="outline" class="manageUsers mr-2">
          <mat-label>Middle Name</mat-label>
          <input type="text" formControlName="middleName" matInput readonly>
        </mat-form-field>
        <mat-form-field appearance="outline" class="manageUsers">
          <mat-label>Last Name</mat-label>
          <input type="text" formControlName="lastName" matInput readonly>
        </mat-form-field>
      </div>

      <mat-form-field appearance="outline" class="w-full">
        <mat-label>Email</mat-label>
        <input type="text" formControlName="email" matInput readonly>
      </mat-form-field>
      <mat-form-field appearance="outline" class="w-full">
        <mat-label>User Role</mat-label>
        <mat-select (selectionChange)="selectedRole($event)" formControlName="role" multiple>
          <mat-option *ngFor="let role of userRoles" [value]="role.id">{{role.roleName}}</mat-option>
        </mat-select>
        <mat-error *ngIf="form.controls['role'].hasError('required')">
          User Role is required
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="w-full">
        <mat-label>
          {{form.value.role == Roles.TrainingInstitue||(Array.isArray(form.value.role) &&
          form.value.role.includes(Roles.TrainingInstitue)) ? 'Training Institute' : 'State' }}
        </mat-label>
        <mat-select formControlName="tenantId" (selectionChange)="state($event)">
          <mat-option *ngFor="let state of statesOrTrainingIns" [value]="state.id">{{state.stateName ? state.stateName :
            state.name}}</mat-option>
        </mat-select>
      </mat-form-field>

      <mat-form-field *ngIf="showTrainingInstitute" appearance="outline" class="w-full">
        <mat-label>Training Institute</mat-label>
        <input type="text" matInput [formControl]="trainingInstituteControl" [matAutocomplete]="trainingInstituteAuto"
          placeholder="Type to search training institutes...">
        <mat-autocomplete #trainingInstituteAuto="matAutocomplete" [displayWith]="displayTrainingInstitute"
          (optionSelected)="onTrainingInstituteSelected($event.option.value)">
          <mat-option *ngFor="let institute of filteredTrainingInstitutes | async" [value]="institute">
            {{institute.name || institute.instituteName}}
          </mat-option>
        </mat-autocomplete>
        <mat-error *ngIf="form.controls['trainingInstitute'].hasError('required')">
          Training Institute is required
        </mat-error>
      </mat-form-field>

    </ng-container>
    <div #ButtonDiv class="flex justify-end pb-2">
      <button class="btn-2 t-xs" type="submit" [disabled]="form.invalid" primary mat-button
        *ngFor="let button of buttons; let btnIndex = index" (click)="submit(button.requestDetails,btnIndex)">
        <span>{{button.buttonText}}</span>
      </button>
    </div>
  </form>
</div>